import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/local/local_storage.dart';
import 'package:vcc/domain/entities/popup/popup_entity.dart';
import 'package:vcc/domain/enums/user_company_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/dialogs/labor_protection/labor_protection_view.dart';
import 'package:vcc/presentation/views/features/home/<USER>/csat_chart_widget.dart';
import 'package:vcc/presentation/views/features/home/<USER>';
import 'package:vcc/presentation/views/features/popup/popup_view_model.dart';
import 'package:vcc/presentation/views/features/store/store_view_model.dart';
import 'package:vcc/domain/entities/alert/alert_entity.dart';
import 'package:vcc/presentation/views/features/home/<USER>';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/bottom_sheet/alert_bottom_sheet.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/notification_badge/notification_badge_widget.dart';
import 'package:vcc/utils/log_utils.dart';
import 'package:vcc/utils/string_utils.dart';

class HomePage extends StatefulHookConsumerWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  @override
  void initState() {
    super.initState();
    Future(() {
      ref.read(homeProvider.notifier).getCountOrders();
      ref.read(homeProvider.notifier).getTotalCompleteOrders();
      ref.read(homeAlertProvider.notifier).fetchAlerts();

      // Show popups after a delay to ensure UI is ready
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _showAllPopupsSequentially();
        }
      });
    });
    ref.read(storeProvider.notifier).getConfigDocument();
  }

  void _showAlertsBottomSheet(BottomSheetAlertsResponse alerts) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AlertBottomSheet(
        alertsResponse: alerts,
        onClose: () {
          Navigator.of(context).pop();
          ref.read(homeAlertProvider.notifier).clearAlert();
        },
      ),
    ).then((_) {
      ref.read(homeAlertProvider.notifier).clearAlert();
    });
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(homeProvider);

    // Listen for alerts and show bottom sheet
    ref.listen<BottomSheetAlertsResponse?>(homeAlertProvider, (previous, current) {
      if (current != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showAlertsBottomSheet(current);
        });
      }
    });

    return LayoutPage(
      backgroundColor: BaseColors.backgroundWhite,
      appbar: AppBarCustom(
        title: "AIO Partner",
        centerTitle: false,
        isShowNavigation: false,
        actionWidget: [
          const NotificationBadgeWidget(
            showLoadingIndicator: true,
          ),
          const SizedBox(width: 16),
          if(GlobalData.instance.userInfo?.isTimekeepingIconVisible ?? false) ...[
            InkWellWidget(
              onTap: () {
                context.push(
                  RouterPaths.timekeeping,
                );
              },
              child: MyAssets.icons.iconCalendarHome.svg(),
            ),
            const SizedBox(width: 16),
          ],
        ],
      ),
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () async {
      //     final result = await MobileCallFunc.startCall(
      //       callerPhoneNumber: "0344849988",
      //       callerName: "Trần Văn Hiệp",
      //       customerNumber: "0971235701",
      //       customerName: "Vu Thanh Cong",
      //       code: "",
      //       content: "",
      //     );
      //
      //     if (result != null) {
      //       print(result.toJson());
      //       // MobileCallRequest request = MobileCallRequest(
      //       //   callType: 1,
      //       //   status: resultCall.status,
      //       //   callerNumber: resultCall.callerNumber,
      //       //   callID: resultCall.callID,
      //       //   duration: resultCall.duration,
      //       //   endTime: Platform.isIOS
      //       //       ? DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.endTime * 1000).round())
      //       //       : DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.endTime)),
      //       //   errorType: resultCall.errorType,
      //       //   ringingTime: Platform.isIOS
      //       //       ? DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.ringingTime * 1000).round())
      //       //       : DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.ringingTime)),
      //       //   startTime: Platform.isIOS
      //       //       ? DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.startTime * 1000).round())
      //       //       : DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.startTime)),
      //       //   state: detailContactController.currentContact?.value?.state,
      //       //   tangentCustomerId: detailContactController.currentContact?.value?.tangentCustomerId,
      //       //   type: "TANGENT_CUSTOMER",
      //       // );
      //
      //       // detailContactController.postResultMobileCall(request);
      //     }
      //   },
      //   child: const Icon(Icons.call),
      // ),
      body: ListView(
        physics: const ClampingScrollPhysics(),
        children: <Widget>[
          Container(
            decoration: BoxDecoration(
              color: BaseColors.backgroundWhite,
              borderRadius: BorderRadius.circular(12),
              boxShadow: AppBoxShadows.shadowCard,
            ),
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            "Thống kê",
                            style: UITextStyle.body1SemiBold.copyWith(
                              color: BaseColors.textBody,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            "Theo ngày hoàn thành",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                const DividerWidget(),
                ListView.separated(
                  itemCount: state.completeOrders?.length ?? 0,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.only(top: 16),
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (context, index) {
                    final item = state.completeOrders![index];

                    return Container(
                      decoration: BoxDecoration(
                        color: BaseColors.backgroundGray,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  item.orderType?.label ?? "",
                                  style: UITextStyle.body2Medium.copyWith(
                                    color: BaseColors.textLabel,
                                  ),
                                ),
                                Text(
                                  "${item.count ?? 0} đơn",
                                  style: UITextStyle.body2Regular.copyWith(
                                    color: BaseColors.textBody,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Text(
                            StringUtils.formatMoney(item.amount ?? 0),
                            style: UITextStyle.body2SemiBold.copyWith(
                              color: BaseColors.primary,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: BaseColors.backgroundWhite,
              borderRadius: BorderRadius.circular(12),
              boxShadow: AppBoxShadows.shadowCard,
            ),
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  "Đơn hàng thường",
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textBody,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  "Thống kê theo ngày tạo đơn",
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.textSubtitle,
                  ),
                ),
                const SizedBox(height: 12),
                const DividerWidget(),
                Container(
                  height: 160,
                  width: double.infinity,
                  margin: const EdgeInsets.only(
                    top: 16,
                    bottom: 24,
                  ),
                  alignment: Alignment.center,
                  child: PieChart(
                    PieChartData(
                      borderData: FlBorderData(
                        show: false,
                      ),
                      sectionsSpace: 0,
                      centerSpaceRadius: 0,
                      sections: state.listChart,
                    ),
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                    ),
                    child: Wrap(
                      direction: Axis.horizontal,
                      alignment: WrapAlignment.start,
                      crossAxisAlignment: WrapCrossAlignment.start,
                      spacing: 8,
                      runSpacing: 8,
                      children: (state.listChart ?? []).map<Widget>(
                        (item) {
                          return SizedBox(
                            width: (MediaQuery.of(context).size.width - 72) / 2,
                            child: Row(
                              children: <Widget>[
                                Container(
                                  height: 8,
                                  width: 8,
                                  decoration: BoxDecoration(
                                    color: item.color,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    item.title,
                                    style: UITextStyle.body2Regular.copyWith(
                                      color: BaseColors.textBody,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const CsatChartWidget(),
        ],
      ),
    );
  }

  // Popup logic moved from root_page
  void _showAllPopupsSequentially() async {
    await _showPopUpSafety(context);

    await Future.delayed(const Duration(milliseconds: 500));

    if (mounted) {
      _showNotificationPopups();
    }
  }

  Future<void> _showPopUpSafety(BuildContext context) async {
    final userInfo = GlobalData.instance.userInfo;

    bool isCheck = await _checkingShowSafetyPolicy();

    if (userInfo != null && isCheck) {
      if (userInfo.userType == UserCompanyType.internal) {
        if (!context.mounted) return;
        await showDialog(
          context: context,
          barrierDismissible: true,
          builder: (BuildContext context) {
            return Dialog(
              insetPadding: const EdgeInsets.all(0),
              backgroundColor: BaseColors.backgroundWhite,
              child: LaborProtectionView(
                isMorning: _isMorningShift(DateTime.now()),
                isAfternoon: _isAfternoonShift(DateTime.now()),
              ),
            );
          },
        );
      }
    }
  }

  void _showNotificationPopups() async {
    try {
      await ref.read(popupProvider.notifier).loadPopups();

      if (!mounted) return;

      final popupState = ref.read(popupProvider);
      if (popupState.popups.isNotEmpty) {
        // Show all popups simultaneously with stacking effect
        _showAllPopupsSimultaneously(popupState.popups);
      }
    } catch (e) {
      LogUtils.e('HomePage: Error loading popups: $e');
    }
  }

  void _showAllPopupsSimultaneously(List<PopupEntity> popups) {
    if (popups.isEmpty || !mounted) return;

    for (int i = popups.length - 1; i >= 0; i--) {
      final popup = popups[i];

      final offsetX = (popups.length - 1 - i) * 4.0;
      final offsetY = (popups.length - 1 - i) * 4.0;

      AppDialog.showPopupNotification(
        context,
        title: popup.title,
        content: popup.content,
        imageUrl: popup.imageUrl,
        link: popup.link,
        directPath: popup.directPath,
        offsetX: offsetX,
        offsetY: offsetY,
      );
    }
  }

  bool _isMorningShift(DateTime now) {
    final h = now.hour;
    final m = now.minute;
    return (h > 6 || (h == 6 && m >= 0)) && (h < 12 || (h == 12 && m == 0));
  }

  bool _isAfternoonShift(DateTime now) {
    final h = now.hour;
    final m = now.minute;
    return (h > 12 || (h == 12 && m >= 0)) && (h < 17 || (h == 17 && m <= 30));
  }

  Future<bool> _checkingShowSafetyPolicy() async {
    LocalStorage localStorage = LocalStorage();

    final status = await localStorage.loadPolicyStatus();
    final now = DateTime.now();

    if (_isMorningShift(now) && !(status?.checkMorning ?? false)) {
      return true;
    }

    if (_isAfternoonShift(now) && !(status?.checkAfternoon ?? false)) {
      return true;
    }

    return false;
  }
}
