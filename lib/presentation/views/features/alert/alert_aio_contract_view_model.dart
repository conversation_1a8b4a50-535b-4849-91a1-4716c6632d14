import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/enums/alert_deadline_type_enum.dart';
import 'package:vcc/domain/enums/load_status.dart';

part 'alert_aio_contract_state.dart';

final alertAioContractProvider =
    StateNotifierProvider.autoDispose<AlertAioContractViewModel, AlertAioContractState>(
  (ref) => AlertAioContractViewModel(ref: ref),
);

class AlertAioContractViewModel extends StateNotifier<AlertAioContractState> {
  final Ref ref;

  AlertAioContractViewModel({
    required this.ref,
  }) : super(const AlertAioContractState());

  Future<void> getData({
    AlertDeadlineType alertDeadlineType = AlertDeadlineType.upcoming,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      alertDeadlineType: alertDeadlineType,
    );
    
    try {
      final result = await appLocator<AioOrderRepository>().getPricingPackageOrdersAlert(
        alertDeadlineType: alertDeadlineType.value,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            listAioContract: data.data,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
        message: 'Đã có lỗi xảy ra',
      );
    }
  }

  void changeAlertDeadlineType(AlertDeadlineType alertDeadlineType) {
    if (state.alertDeadlineType != alertDeadlineType) {
      getData(alertDeadlineType: alertDeadlineType);
    }
  }
}
