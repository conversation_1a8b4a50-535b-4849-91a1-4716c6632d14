import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/alert_deadline_type_enum.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_package/aio_contract_package_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/widgets/aio_contract_item_widget.dart';
import 'package:vcc/presentation/views/features/alert/alert_aio_contract_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';

class AlertAioContractTabPage extends HookConsumerWidget {
  final AlertDeadlineType? alertDeadlineType;

  const AlertAioContractTabPage({
    super.key,
    this.alertDeadlineType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deadlineType = alertDeadlineType ?? AlertDeadlineType.upcoming;

    useEffect(() {
      Future(() {
        ref.read(alertAioContractProvider.notifier).getData(
          alertDeadlineType: deadlineType,
        );
      });
      return null;
    }, [deadlineType]);

    return _buildContractList(context, ref, deadlineType);
  }

  Widget _buildContractList(BuildContext context, WidgetRef ref, AlertDeadlineType deadlineType) {
    final state = ref.watch(alertAioContractProvider);

    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }

    if ((state.listAioContract ?? []).isEmpty) {
      return EmptyListWidget(
        title: "Không có dữ liệu",
        isFullHeight: false,
        heightSpacerTop: 100,
        onRefresh: () async {
          ref.read(alertAioContractProvider.notifier).getData(
            alertDeadlineType: deadlineType,
          );
        },
      );
    }

    return Container(
      color: BaseColors.backgroundGray,
      child: RefreshIndicatorWidget(
        onRefresh: () async {
          ref.read(alertAioContractProvider.notifier).getData(
            alertDeadlineType: deadlineType,
          );
        },
        child: ListView.builder(
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: state.listAioContract?.length ?? 0,
          itemBuilder: (context, index) {
            final item = state.listAioContract![index];

            return AioContractItemWidget(
              item: item,
              onTap: () async {
                if (item.isPaying == 1) {
                  AppDialog.showDialogConfirm(
                    context,
                    title: "Chú ý",
                    message:
                        "Hiện tại hợp đồng (${item.contractCode}) đang được thực hiện trên một giao dịch khác. Bạn có muốn tiếp tục không?",
                    buttonNameConfirm: "Xác nhận",
                    onConfirmAction: () async {
                      await context.push(
                        RouterPaths.aioContractPackage,
                        extra: AioContractPackageArguments(
                          aioContractEntity: item,
                          backFunction: () {
                            // Refresh data when coming back from detail
                            ref
                                .read(alertAioContractProvider.notifier)
                                .getData(alertDeadlineType: deadlineType);
                          },
                        ),
                      );
                    },
                  );
                  return;
                }

                await context.push(
                  RouterPaths.aioContractPackage,
                  extra: AioContractPackageArguments(
                    aioContractEntity: item,
                    backFunction: () {
                      ref
                          .read(alertAioContractProvider.notifier)
                          .getData(alertDeadlineType: deadlineType);
                    },
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
