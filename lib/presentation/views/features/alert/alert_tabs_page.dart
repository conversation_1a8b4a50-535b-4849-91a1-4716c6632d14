import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/alert/alert_entity.dart';
import 'package:vcc/domain/enums/alert_deadline_type_enum.dart';
import 'package:vcc/domain/enums/alert_type_enum.dart';
import 'package:vcc/presentation/views/features/alert/alert_aio_contract_tab_page.dart';
import 'package:vcc/presentation/views/features/alert/alert_complain_tab_page.dart';
import 'package:vcc/presentation/views/features/alert/alert_explanation_tab_page.dart';
import 'package:vcc/presentation/views/features/alert/alert_orders_provider.dart';
import 'package:vcc/presentation/views/features/order/list_order/widgets/order_item.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';

class AlertTabsPage extends HookConsumerWidget {
  final BottomSheetAlertsResponse alertsResponse;
  final int initialTabIndex;

  const AlertTabsPage({
    super.key,
    required this.alertsResponse,
    required this.initialTabIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activeAlerts = alertsResponse.activeAlerts;
    final tabController = useTabController(
      initialLength: activeAlerts.length,
      initialIndex: initialTabIndex,
    );
    final selectedFilterIndex = useState(0); // 0: Quá hạn, 1: Trong hạn

    useEffect(() {
      if (activeAlerts.isNotEmpty) {
        final initialAlert = activeAlerts[initialTabIndex];
        final alertType = initialAlert.key;

        Future(() {
          final alertTypeEnum = AlertType.fromValue(alertType);
          if (alertTypeEnum != null && alertTypeEnum.supportsDeadlineFilter) {
            final deadlineType = selectedFilterIndex.value == 0
                ? AlertDeadlineType.overdue
                : AlertDeadlineType.upcoming;
            ref.read(alertOrdersProvider.notifier).loadOrders(
                  alertType: alertType,
                  alertDeadlineType: deadlineType.value,
                );
          }
        });
      }
      return null;
    }, []);

    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      appBar: AppBarCustom(
        title: 'Cảnh báo cần xử lý',
        actionWidget: [
          _buildFilterButton(
              context, selectedFilterIndex, ref, activeAlerts, tabController),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(tabController, activeAlerts, selectedFilterIndex, ref),
          Expanded(
            child: TabBarView(
              controller: tabController,
              children: activeAlerts.map((alertEntry) {
                final alertType = alertEntry.key;
                final alertTypeEnum = AlertType.fromValue(alertType);

                // Use custom tab pages for specific alert types
                if (alertTypeEnum == AlertType.explanation) {
                  return const AlertExplanationTabPage();
                }

                if (alertTypeEnum == AlertType.complaintFeedback) {
                  return const AlertComplainTabPage();
                }

                if (alertTypeEnum == AlertType.pricingPackageOrder) {
                  final deadlineType = selectedFilterIndex.value == 0
                      ? AlertDeadlineType.overdue
                      : AlertDeadlineType.upcoming;
                  return AlertAioContractTabPage(
                    alertDeadlineType: deadlineType,
                  );
                }

                // Use OrderItem list for other tabs
                final deadlineType = selectedFilterIndex.value == 0
                    ? AlertDeadlineType.overdue
                    : AlertDeadlineType.upcoming;
                return _buildOrdersList(ref, alertType, deadlineType.value);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton(
    BuildContext context,
    ValueNotifier<int> selectedFilterIndex,
    WidgetRef ref,
    List<MapEntry<String, AlertEntity>> activeAlerts,
    TabController tabController,
  ) {
    return PopupMenuButton<int>(
      icon: Icon(
        Icons.filter_list,
        color: BaseColors.backgroundWhite,
      ),
      onSelected: (value) {
        selectedFilterIndex.value = value;
        final deadlineType =
            value == 0 ? AlertDeadlineType.overdue : AlertDeadlineType.upcoming;

        // Load data for current active tab
        final currentTabIndex = tabController.index;
        if (currentTabIndex < activeAlerts.length) {
          final currentAlert = activeAlerts[currentTabIndex];
          final alertType = currentAlert.key;
          final alertTypeEnum = AlertType.fromValue(alertType);

          if (alertTypeEnum != null && alertTypeEnum.supportsDeadlineFilter) {
            ref.read(alertOrdersProvider.notifier).loadOrders(
                  alertType: alertType,
                  alertDeadlineType: deadlineType.value,
                );
          }
        }
      },
      itemBuilder: (context) =>
          AlertDeadlineType.values.asMap().entries.map((entry) {
        final index = entry.key;
        final deadlineType = entry.value;

        return PopupMenuItem(
          value: index,
          child: Row(
            children: [
              Icon(
                selectedFilterIndex.value == index
                    ? Icons.radio_button_checked
                    : Icons.radio_button_unchecked,
                color: selectedFilterIndex.value == index
                    ? BaseColors.primary
                    : BaseColors.textSubtitle,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                deadlineType.displayName,
                style: UITextStyle.body2Regular.copyWith(
                  color: selectedFilterIndex.value == index
                      ? BaseColors.primary
                      : BaseColors.textTitle,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTabBar(
    TabController tabController,
    List<MapEntry<String, AlertEntity>> activeAlerts,
    ValueNotifier<int> selectedFilterIndex,
    WidgetRef ref,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundWhite,
        border: Border(
          bottom: BorderSide(
            color: BaseColors.backgroundGray,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: tabController,
        labelColor: BaseColors.primary,
        unselectedLabelColor: BaseColors.textSubtitle,
        labelStyle: UITextStyle.body2SemiBold,
        unselectedLabelStyle: UITextStyle.body2Regular,
        indicatorColor: BaseColors.primary,
        isScrollable: activeAlerts.length > 3,
        // Make scrollable if more than 3 tabs
        tabAlignment:
            activeAlerts.length <= 3 ? TabAlignment.fill : TabAlignment.start,
        // Fill space when <= 3 tabs
        labelPadding: EdgeInsets.zero,
        // Remove default label padding
        indicatorPadding: EdgeInsets.zero,
        // Remove default indicator padding
        padding: EdgeInsets.zero,
        // Remove default TabBar padding
        onTap: (index) {
          final alertEntry = activeAlerts[index];
          final alertType = alertEntry.key;

          Future(() {
            final alertTypeEnum = AlertType.fromValue(alertType);
            if (alertTypeEnum != null && alertTypeEnum.supportsDeadlineFilter) {
              final deadlineType = selectedFilterIndex.value == 0
                  ? AlertDeadlineType.overdue
                  : AlertDeadlineType.upcoming;
              ref.read(alertOrdersProvider.notifier).loadOrders(
                    alertType: alertType,
                    alertDeadlineType: deadlineType.value,
                  );
            }
          });
        },
        tabs: activeAlerts.map((alertEntry) {
          return Tab(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
              child: Text(
                _getShortAlertName(alertEntry.key, alertEntry.value),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  String _getShortAlertName(String alertType, AlertEntity alertEntity) {
    // Priority 1: Use displayName from AlertEntity if available
    if (alertEntity.displayName != null &&
        alertEntity.displayName!.isNotEmpty) {
      return alertEntity.displayName!;
    }

    // Priority 2: Use enum shortName as fallback
    final alertTypeEnum = AlertType.fromValue(alertType);
    return alertTypeEnum?.shortName ?? alertType;
  }

  Widget _buildOrdersList(
      WidgetRef ref, String alertType, String deadlineType) {
    final state = ref.watch(alertOrdersProvider);

    if (state.isLoading) {
      return const LoadingIndicatorWidget();
    }

    if (state.orders.isEmpty) {
      return Container(
        color: BaseColors.backgroundGray,
        child: EmptyListWidget(
          title: "Không có dữ liệu",
          onRefresh: () async {
            ref.read(alertOrdersProvider.notifier).loadOrders(
                  alertType: alertType,
                  alertDeadlineType: deadlineType,
                );
          },
        ),
      );
    }

    return Container(
      color: BaseColors.backgroundGray,
      child: RefreshIndicatorWidget(
        onRefresh: () async {
          ref.read(alertOrdersProvider.notifier).loadOrders(
                alertType: alertType,
                alertDeadlineType: deadlineType,
              );
        },
        child: ListView.builder(
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: state.orders.length,
          itemBuilder: (context, index) {
            final order = state.orders[index];
            return OrderItem(
              item: order,
              callBackOnBack: () {
                ref.read(alertOrdersProvider.notifier).loadOrders(
                      alertType: alertType,
                      alertDeadlineType: deadlineType,
                    );
              },
            );
          },
        ),
      ),
    );
  }
}
