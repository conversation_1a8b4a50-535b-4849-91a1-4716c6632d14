part of 'alert_aio_contract_view_model.dart';

class AlertAioContractState extends Equatable {
  final LoadStatus loadStatus;
  final List<AioContractEntity>? listAioContract;
  final String? message;
  final AlertDeadlineType alertDeadlineType;

  const AlertAioContractState({
    this.loadStatus = LoadStatus.initial,
    this.listAioContract,
    this.message,
    this.alertDeadlineType = AlertDeadlineType.upcoming,
  });

  AlertAioContractState copyWith({
    LoadStatus? loadStatus,
    List<AioContractEntity>? listAioContract,
    String? message,
    AlertDeadlineType? alertDeadlineType,
  }) {
    return AlertAioContractState(
      loadStatus: loadStatus ?? this.loadStatus,
      listAioContract: listAioContract ?? this.listAioContract,
      message: message ?? this.message,
      alertDeadlineType: alertDeadlineType ?? this.alertDeadlineType,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        listAioContract,
        message,
        alertDeadlineType,
      ];
}
