import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/app_configs/coordinator.dart';
import 'package:vcc/data/services/firebase/firebase_service.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/user_position_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/features/authentication/login/login_view_model.dart';
import 'package:vcc/presentation/views/features/authentication/setup_account/setup_account_page.dart';
import 'package:vcc/presentation/views/features/root/root_page.dart';
import 'package:vcc/presentation/views/features/store/store_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/pages/web_view_screen.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/show_loading_utils.dart';

class LoginArguments {
  final bool? canBack;
  final String? phoneNumber;
  final String? password;
  final String? returnPath;

  LoginArguments({
    this.canBack,
    this.phoneNumber,
    this.password,
    this.returnPath,
  });
}

class LoginPage extends StatefulHookConsumerWidget {
  final LoginArguments? arguments;

  const LoginPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  String? encryptedText;
  String? publicKeyString;

  late TextEditingController userController;
  late TextEditingController passwordController;

  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    userController = TextEditingController(
      text: '',
    );
    passwordController = TextEditingController(
      text: '',
    );

    // Track screen view
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FirebaseService.instance.analytics.logScreenView(
        screenName: 'login_page',
        screenClass: 'LoginPage',
      );
    });

    super.initState();
  }

  @override
  void dispose() {
    userController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final loginState = ref.watch(loginProvider);

    return LayoutPage(
      backgroundColor: BaseColors.backgroundWhite,
      bottomAction: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Bạn chưa có tài khoản?',
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              const Gap(4),
              Semantics(
                identifier: 'btnRegisterNow',
                child: AppTextButton(
                  title: 'Đăng ký ngay',
                  padding: EdgeInsets.zero,
                  onTap: () {
                    // Track button click
                    FirebaseService.instance.analytics.logButtonClick(
                      buttonName: 'register_now',
                      screenName: 'login_page',
                    );

                    // context.push(RouterPaths.register);
                    context.push(
                      RouterPaths.setupAccount,
                      extra: SetupAccountArguments(),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 80),
                    MyAssets.images.imgLogoS80.image(),
                    const SizedBox(height: 12),
                    Text(
                      "AIO Partner",
                      style: UITextStyle.body2SemiBold,
                      textAlign: TextAlign.center,
                    ),
                    const Gap(BaseSpacing.spacing10),
                    TextFieldWidget(
                      controller: userController,
                      prefix: MyAssets.icons.user.svg(),
                      labelText: 'Tên đăng nhập',
                      textInputAction: TextInputAction.done,
                      validator: (value) {
                        if (value.isEmpty) {
                          return 'Tên đăng nhập không được để trống.';
                        }
                        return null;
                      },
                    ),
                    const Gap(BaseSpacing.spacing4),
                    TextFieldWidget(
                      controller: passwordController,
                      labelText: 'Mật khẩu',
                      prefix: MyAssets.icons.unlockOtp.svg(),
                      keyboardType: TextInputType.visiblePassword,
                      textInputAction: TextInputAction.done,
                      suffix: InkWell(
                        onTap: () {
                          ref
                              .read(loginProvider.notifier)
                              .changeObscureStatus();
                        },
                        child: loginState.isObscureText
                            ? MyAssets.icons.eye.svg()
                            : MyAssets.icons.eyeSlash.svg(),
                      ),
                      obscureText: loginState.isObscureText,
                      validator: (value) {
                        if (value.isEmpty) {
                          return 'Mật khẩu không được để trống.';
                        }
                        return null;
                      },
                    ),
                    const Gap(BaseSpacing.spacing2),
                    SizedBox(
                      width: double.infinity,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Semantics(
                            identifier: 'btnForgotPassword',
                            child: AppTextButton(
                              title: "Quên mật khẩu",
                              onTap: () {
                                // Track button click
                                FirebaseService.instance.analytics.logButtonClick(
                                  buttonName: 'forgot_password',
                                  screenName: 'login_page',
                                );

                                context.push(RouterPaths.forgotPassword);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Gap(24),
                    RichText(
                      text: TextSpan(
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.textBody,
                        ),
                        children: [
                          const TextSpan(
                            text:
                                "Bằng cách đăng nhập/đăng ký, bạn đã hiểu và đồng ý với ",
                          ),
                          TextSpan(
                            text: "Điều khoản sử dụng",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.info,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                context.push(
                                  RouterPaths.webView,
                                  extra: WebViewArguments(
                                    url: BaseConstant.termUrl,
                                    title: "Điều khoản sử dụng",
                                  ),
                                );
                              },
                          ),
                          const TextSpan(
                            text: ' và ',
                          ),
                          TextSpan(
                            text: "Chính sách bảo mật",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.info,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                context.push(
                                  RouterPaths.webView,
                                  extra: WebViewArguments(
                                    url: BaseConstant.termUrl,
                                    title: "Chính sách bảo mật",
                                  ),
                                );
                              },
                          ),
                          const TextSpan(
                            text: " của Viettel Construction.",
                          ),
                        ],
                      ),
                    ),
                    const Gap(20),
                    Semantics(
                      identifier: 'btnLogin',
                      child: BaseButton(
                        text: 'Đăng nhập',
                        onTap: () async {
                          final form = formKey.currentState;
                          if (form!.validate()) {
                            try {
                              // Track login button click
                              await FirebaseService.instance.analytics.logButtonClick(
                                buttonName: 'login_submit',
                                screenName: 'login_page',
                              );

                              ShowLoadingUtils.instance.turnOn();
                              await ref
                                  .read(loginProvider.notifier)
                                  .login(
                                    userName: userController.text,
                                    password: passwordController.text,
                                  )
                                  .whenComplete(
                                () async {
                                  ShowLoadingUtils.instance.turnOff();
                                  if (ref.watch(loginProvider).loadStatus ==
                                      LoadStatus.success) {
                                    ref.read(storeProvider).addressDefault = null;
                                    String? position = await ref
                                        .read(loginProvider.notifier)
                                        .getProfile();
                                    if (!context.mounted) return;

                                    // Handle return path after successful login
                                    final returnPath = widget.arguments?.returnPath;
                                    if (returnPath != null && returnPath.isNotEmpty) {
                                      context.go(returnPath);
                                      return;
                                    }

                                    // Default navigation logic
                                    if (position ==
                                        UserPositionType.agency.keyToServer) {
                                      AppCoordinator.context.go(
                                        RouterPaths.root,
                                        extra: RootArguments(
                                          pageIndex: 0,
                                          isAgency: true,
                                        ),
                                      );
                                    } else {
                                      context.pushReplacement(
                                        RouterPaths.root,
                                      );
                                    }
                                  }
                                },
                              );
                            } catch (error, stackTrace) {
                              // Log unexpected UI error
                              await FirebaseService.instance.recordError(
                                error,
                                stackTrace,
                                reason: 'Login UI error',
                                customKeys: {
                                  'screen': 'login_page',
                                  'action': 'login_button_tap',
                                },
                              );
                              ShowLoadingUtils.instance.turnOff();
                            }
                          } else {
                            // Track validation error
                            await FirebaseService.instance.analytics.logEvent(
                              eventName: 'form_validation_failed',
                              parameters: {
                                'screen_name': 'login_page',
                                'form_type': 'login_form',
                                'timestamp': DateTime.now().millisecondsSinceEpoch,
                              },
                            );
                            ref.read(loginProvider.notifier).reloadUI();
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              child: InkWellWidget(
                onTap: () {
                  context.pop();
                  ShowLoadingUtils.instance.turnOff();
                },
                child: MyAssets.icons.iconBackGraySquare.svg(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
