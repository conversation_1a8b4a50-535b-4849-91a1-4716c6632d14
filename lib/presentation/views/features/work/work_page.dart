import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/entities/sales_team/sale_team_info_entity.dart';
import 'package:vcc/domain/entities/work/work_menu_entity.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/sale_group/sale_group_member_status.dart';
import 'package:vcc/domain/enums/work_menu_type.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/features/agency/work_order_agency/work_agency_page.dart';
import 'package:vcc/presentation/views/features/document/document_screen.dart';
import 'package:vcc/presentation/views/features/order/list_order/list_order_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_page.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_page.dart';
import 'package:vcc/presentation/views/features/work/calendar/calendar_page.dart';
import 'package:vcc/presentation/views/features/work/confirm_commission/confirm_commission_page.dart';
import 'package:vcc/presentation/views/features/work/explanation/explanation_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/stock_export_page.dart';
import 'package:vcc/presentation/views/features/work/work_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/auth/auth_validation_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/sliver_grid_fixed_height.dart';

class WorkPage extends StatefulHookConsumerWidget {
  const WorkPage({
    super.key,
  });

  @override
  ConsumerState<WorkPage> createState() => _WorkPageState();
}

class _WorkPageState extends ConsumerState<WorkPage> {
  @override
  void initState() {
    Future(() {
      ref.read(workProvider.notifier).getMenu();
      ref.read(workProvider.notifier).getWorks();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(workProvider);
    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Quản lý công việc",
        isShowNavigation: false,
      ),
      body: _buildListWork(state),
    );
  }

  Widget _buildListWork(WorkState state) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: state.menu?.length ?? 0,
      itemBuilder: (context, index) {
        var itemParent = state.menu?[index];
        var listItem = itemParent?.items ?? [];

        listItem = listItem.where((e) => e.showMenu).toList();

        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    itemParent?.title ?? "",
                    style: UITextStyle.body1SemiBold.copyWith(
                      color: BaseColors.textTitle,
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  GridView.builder(
                    shrinkWrap: true,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
                      crossAxisCount: 4,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      height: 95,
                    ),
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: listItem.length,
                    itemBuilder: (context, indexItem) {
                      var itemChild = listItem[indexItem];

                      return Visibility(
                        visible: itemChild.showMenu,
                        child: InkWell(
                          onTap: () {
                            _selectItem(itemChild);
                          },
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              _getMenuIcon(itemChild.icon ?? ''),
                              const SizedBox(
                                height: 4,
                              ),
                              Text(
                                itemChild.title ?? "",
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: UITextStyle.body2Regular.copyWith(
                                  color: BaseColors.textLabel,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            DividerWidget(
              color: BaseColors.backgroundGray,
              height: 8,
            ),
          ],
        );
      },
    );
  }

  _selectItem(WorkMenuEntity? item) async {
    if (item?.code == WorkMenuType.orderImplementation.value ||
        item?.code == WorkMenuType.deliverOrder.value ||
        item?.code == WorkMenuType.allOrder.value) {
      List<OrderType>? orderTypes = [];

      if (item?.code == WorkMenuType.orderImplementation.value) {
        orderTypes = [
          OrderType.service,
          OrderType.supply,
          OrderType.package,
          OrderType.combo,
          OrderType.maintenance,
          OrderType.salePoint,
          OrderType.salePointSingle,
          OrderType.salePointCombo,
          OrderType.partnerWarrantyService,
          OrderType.partnerNotWarrantyService,
          OrderType.partnerOperate,
          OrderType.partnerMaintenance,
          OrderType.partnerResolveProblem,
        ];
      } else if (item?.code == WorkMenuType.deliverOrder.value) {
        orderTypes = [
          OrderType.smart,
        ];
      }

      context.push(
        RouterPaths.allOrder,
        extra: ListOrderArguments(
          orderTypes: orderTypes,
        ),
      );
    } else if (item?.code == WorkMenuType.mngAssociation.value) {
      context.push(
        RouterPaths.allAssociation,
      );
    } else if (item?.code == WorkMenuType.collectionInfoW.value) {
      context.push(
        RouterPaths.collectionInfoWarehouse,
      );
    } else if (item?.code == WorkMenuType.allAssociationWO.value) {
      context.push(
        RouterPaths.allAssociationWO,
      );
    } else if (item?.code == WorkMenuType.debtStatistics.value) {
      context.push(
        RouterPaths.debtStatistics,
      );
    } else if (item?.code == WorkMenuType.requestBuyProduct.value) {
      context.push(
        RouterPaths.requestBuyProduct,
      );
    } else if (item?.code == WorkMenuType.confirmCommission.value) {
      context.push(
        RouterPaths.confirmCommission,
        extra: ConfirmCommissionArguments(data: ""),
      );
    } else if (item?.code == WorkMenuType.bill.value) {
      context.push(
        RouterPaths.bill,
        extra: BillArguments(data: ""),
      );
    } else if (item?.code == WorkMenuType.salesTeam.value) {
      final result = await ref.read(workProvider.notifier).getMyTeam();
      navigateSaleTeamScreen(result);
    } else if (item?.code == WorkMenuType.stockExport.value) {
      context.push(
        RouterPaths.stockExport,
        extra: StockExportArguments(data: ""),
      );
    } else if (item?.code == WorkMenuType.explanation.value) {
      context.push(
        RouterPaths.explanation,
        extra: ExplanationArguments(data: ""),
      );
    } else if (item?.code == WorkMenuType.calendar.value) {
      context.push(
        RouterPaths.calendar,
        extra: CalendarArguments(data: ""),
      );
    } else
    // if (item?.code == WorkMenuType.purchaseRequest.value) {
    //   context.push(
    //     RouterPaths.purchaseRequest,
    //     extra: PurchaseRequestArguments(data: ""),
    //   );
    // }
    if (item?.code == WorkMenuType.contract.value) {
      context.push(
        RouterPaths.aioContract,
        extra: AioContractArguments(data: ""),
      );
    } else if (item?.code == WorkMenuType.contractSale.value) {
      context.push(
        RouterPaths.aioContract,
        extra: AioContractArguments(data: WorkMenuType.contractSale.value),
      );
    } else if (item?.code == WorkMenuType.reportError.value) {
      context.push(
        RouterPaths.allReportError,
      );
    } else if (item?.code == WorkMenuType.report.value) {
      context.push(
        RouterPaths.complain,
      );
    } else if (item?.code == WorkMenuType.warranty.value) {
      context.push(
        RouterPaths.requirementWarranty,
      );
    } else if (item?.code == WorkMenuType.documentSale.value) {
      context.push(
        RouterPaths.webViewDocument,
        extra: DocumentArguments(
          url: BaseConstant.saleDocument,
          title: "Tài liệu bán hàng",
        ),
      );
    } else if (item?.code == WorkMenuType.manageCollaborator.value) {
      context.push(
        RouterPaths.manageCollaborator,
      );
    } else if (item?.code == WorkMenuType.orderAgency.value) {
      context.push(
        RouterPaths.workAgencyPage,
        extra: WorkAgencyArgument(
          isFromManageOrder: true,
        ),
      );
    } else if (item?.code == WorkMenuType.quote.value) {
      context.push(
        RouterPaths.customerQuoteHome,
      );
    } else if (item?.code == WorkMenuType.contractDrafting.value) {
      context.push(
        RouterPaths.contractDraftPage,
      );
    }
    if (item?.code == WorkMenuType.feedback.value) {
      context.push(
        RouterPaths.listFeedback,
      );
    }
  }

  void navigateSaleTeamScreen(SaleTeamInfoEntity? result) {
    if (result != null) {
      if (GlobalData.instance.userInfo?.saleGroupType != null) {
        context.push(
          RouterPaths.saleTeamDetail,
        );
      } else {
        if (result.memberStatus == SaleGroupMemberStatus.active) {
          context.push(
            RouterPaths.saleTeamDetail,
          );
        } else {
          context.push(
            RouterPaths.saleTeamHome,
          );
        }
      }
    } else {
      context.push(
        RouterPaths.saleTeamHome,
      );
    }
  }

  _getMenuIcon(String icon) {
    return SvgPicture.asset('assets/icons/$icon.svg');
  }
}
