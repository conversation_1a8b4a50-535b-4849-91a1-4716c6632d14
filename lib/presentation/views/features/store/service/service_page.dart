import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/create_service_order_page.dart';
import 'package:vcc/presentation/views/features/store/service/service_view_model.dart';
import 'package:vcc/presentation/views/features/store/service/widgets/supply_item_widget.dart';
import 'package:vcc/presentation/views/features/store/store_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/hide_button.dart';
import 'package:vcc/utils/app_utils.dart';

import 'widgets/service_package_item_widget.dart';
import 'widgets/service_single_item_widget.dart';

class ServicePage extends StatefulHookConsumerWidget {
  const ServicePage({
    super.key,
  });

  @override
  ConsumerState<ServicePage> createState() => _ServicePageState();
}

class _ServicePageState extends ConsumerState<ServicePage> {
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    Future(() {
      if (ref.watch(storeProvider).addressDefault != null) {
        ref.read(serviceProvider.notifier).initData();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: BaseColors.backgroundWhite,
      child: _buildListService(),
    );
  }

  Widget _buildListService() {
    var serviceState = ref.watch(serviceProvider);

    return Semantics(
      identifier: "servicePageContent",
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Semantics(
                identifier: "txtSingleServiceTitle",
                child: Text(
                  'Dịch vụ lẻ',
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
            ),
            Semantics(
              identifier: "singleServiceSection",
              child: _buildSingleService(serviceState),
            ),
            DividerWidget(
              height: 4,
              color: BaseColors.backgroundGray,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      'Dịch vụ gói',
                      style: UITextStyle.body1SemiBold.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  Semantics(
                    identifier: 'btnViewAllPackageService',
                    child: AppTextButton(
                      title: "Xem tất cả",
                      titleStyle: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                      iconRight: MyAssets.icons.iconArrowRightS20.svg(),
                      padding: const EdgeInsets.fromLTRB(8, 8, 0, 8),
                      onTap: () {
                        context.push(
                          RouterPaths.listPackageService,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            _buildPackageService(serviceState),
            const SizedBox(
              height: 16,
            ),
            if ((serviceState.supplies ?? []).isNotEmpty) ...[
              DividerWidget(
                height: 4,
                color: BaseColors.backgroundGray,
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: Text(
                        'Dịch vụ vật tư',
                        style: UITextStyle.body1SemiBold.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    HideButton(
                      child: Semantics(
                        identifier: 'btnViewAllSupply',
                        child: AppTextButton(
                          title: "Xem tất cả",
                          titleStyle: UITextStyle.body2Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                          iconRight: MyAssets.icons.iconArrowRightS20.svg(),
                          padding: const EdgeInsets.fromLTRB(8, 8, 0, 8),
                          onTap: () {
                            // context.push(
                            //   RouterPaths.listPackageService,
                            // );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              _buildSupplies(),
              const SizedBox(
                height: 16,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPackageService(ServiceState serviceState) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 240,
      child: ListView.separated(
        controller: scrollController,
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        itemCount: serviceState.services?.length ?? 0,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        separatorBuilder: (_, __) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          return ServicePackageItemWidget(
            item: serviceState.services![index],
            width: 140,
            onTap: () {
              AppUtils.checkLogin(
                context,
                onLogged: () {
                  context.push(
                    RouterPaths.createServiceOrder,
                    extra: CreateServiceOrderArguments(
                      serviceType: ServiceType.package,
                      serviceYearInfo: serviceState.services![index],
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildSingleService(ServiceState serviceState) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
      ),
      height: 230,
      child: SizedBox(
        child: GridView.builder(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: (serviceState.categories?.length ?? 0) > 6
              ? const AlwaysScrollableScrollPhysics()
              : const NeverScrollableScrollPhysics(),
          itemCount: serviceState.categories?.length ?? 0,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 10,
            mainAxisSpacing: 20,
          ),
          itemBuilder: (context, index) {
            ServiceType type =
                ref.read(serviceProvider.notifier).getTypeService(
                      type: serviceState.categories![index].type ?? '',
                      code: serviceState.categories![index].code ?? '',
                    );
            return ServiceSingleItemWidget(
              item: serviceState.categories![index],
              listMenu: serviceState.categories ?? [],
              activeMenu: index,
              type: type,
            );
          },
        ),
      ),
    );
  }

  Widget _buildSupplies() {
    var serviceState = ref.watch(serviceProvider);
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: 240,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        itemCount: serviceState.supplies?.length ?? 0,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        separatorBuilder: (_, __) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          return SupplyItemWidget(
            item: serviceState.supplies![index],
            width: 140,
            onTap: () {
              AppUtils.checkLogin(
                context,
                onLogged: () {
                  context.push(
                    RouterPaths.createServiceOrder,
                    extra: CreateServiceOrderArguments(
                      serviceType: ServiceType.supply,
                      supplyInfo: serviceState.supplies![index],
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
