import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/product_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/select_address_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_product_order/create_product_order_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/create_service_order_page.dart';
import 'package:vcc/presentation/views/features/store/product/product_page.dart';
import 'package:vcc/presentation/views/features/store/service/service_page.dart';
import 'package:vcc/presentation/views/features/store/store_view_model.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/tab_bar_widget.dart';
import 'package:vcc/utils/app_utils.dart';

class StorePage extends StatefulHookConsumerWidget {
  const StorePage({super.key});

  @override
  ConsumerState<StorePage> createState() => _StorePageState();
}

class _StorePageState extends ConsumerState<StorePage>
    with TickerProviderStateMixin {
  List<String> listPage = [
    "Điện tử đồ gia dụng",
    "Dịch vụ bảo dưỡng",
  ];

  late TabController tabController;

  @override
  void initState() {
    tabController = TabController(
      length: listPage.length,
      initialIndex: 0,
      vsync: this,
    );

    Future(() {
      _initData(context);
    });
    super.initState();
  }

  void _initData(BuildContext context) async {
    ref.read(storeProvider.notifier).getDraftOrder();
    await ref.read(storeProvider.notifier).getAddressFromIsar();
    ref.read(storeProvider.notifier).getConfigDocument();
    if (ref.watch(storeProvider).addressDefault == null) {
      if (!context.mounted) return;
      AppBottomSheet.showNormalBottomSheet(
        context,
        title: "Chọn địa chỉ",
        enableDrag: false,
        isDismissible: false,
        height: MediaQuery.of(context).size.height * 0.95,
        rightButton: const SizedBox(),
        child: SelectAddressView(
          onSelectAddress: (address) {
            ref.read(storeProvider.notifier).selectAddress(address);
          },
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    var storeState = ref.watch(storeProvider);

    return LayoutPage(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            color: BaseColors.primary,
            padding: const EdgeInsets.fromLTRB(16, 0, 8, 12),
            child: SafeArea(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Semantics(
                      identifier: "btnSelectStoreAddress",
                      child: InkWellWidget(
                        onTap: () {
                          AppBottomSheet.showNormalBottomSheet(
                            context,
                            title: "Chọn địa chỉ",
                            height: MediaQuery.of(context).size.height * 0.95,
                            child: SelectAddressView(
                              onSelectAddress: (address) {
                                ref
                                    .read(storeProvider.notifier)
                                    .selectAddress(address);
                              },
                            ),
                          );
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Semantics(
                              identifier: "txtViewPriceAt",
                              child: Text(
                                "Xem giá tại",
                                style: UITextStyle.caption1Regular.copyWith(
                                  color: BaseColors.backgroundWhite,
                                ),
                              ),
                            ),
                            Row(
                              children: [
                                Flexible(
                                  child: Semantics(
                                    identifier: "txtSelectedAddress",
                                    child: Text(
                                      storeState.addressDefault != null
                                          ? storeState.addressDefault!
                                              .getDistrictAndProvince()
                                          : "Chọn địa chỉ",
                                      style: UITextStyle.body2SemiBold.copyWith(
                                        color: BaseColors.backgroundWhite,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Semantics(
                                  identifier: "iconDropdownAddress",
                                  child: MyAssets.icons.iconArrowDown.svg(),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  if (storeState.draftOrder != null) ...[
                    Semantics(
                      identifier: "btnDraftOrder",
                      child: InkWellWidget(
                        onTap: () {
                          AppUtils.checkLogin(
                            context,
                            onLogged: () {
                              if (storeState.draftOrder!.type ==
                                  ProductType.product.name) {
                                context.push(
                                  RouterPaths.createProductOrder,
                                  extra: CreateProductOrderArguments(
                                    draftOrder: storeState.draftOrder,
                                    isFromDraftIcon: true,
                                  ),
                                );
                              } else {
                                context.push(
                                  RouterPaths.createServiceOrder,
                                  extra: CreateServiceOrderArguments(
                                    draftOrder: storeState.draftOrder,
                                    isFromDraftIcon: true,
                                  ),
                                );
                              }
                            },
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(6),
                          child: MyAssets.icons.iconNoteSquare.svg(),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                  ],
                  Semantics(
                    identifier: "btnSearchStore",
                    child: InkWellWidget(
                      onTap: () {
                        context.push(RouterPaths.searchStore);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(6),
                        child: MyAssets.icons.iconSearchSquare.svg(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Semantics(
            identifier: "tabBarStore",
            child: TabBarWidget(
              tabItems: listPage,
              tabController: tabController,
            ),
          ),
          Expanded(
            child: Semantics(
              identifier: "tabBarViewStore",
              child: TabBarView(
                key: ValueKey(
                  storeState.addressDefault?.district?.code ?? "0",
                ),
                controller: tabController,
                physics: const NeverScrollableScrollPhysics(),
                children: <Widget>[
                  Semantics(
                    identifier: "tabProductPage",
                    child: const ProductPage(),
                  ),
                  Semantics(
                    identifier: "tabServicePage",
                    child: const ServicePage(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
