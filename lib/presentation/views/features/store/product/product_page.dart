import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/product/category_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/store/product/all_products/all_products_page.dart';
import 'package:vcc/presentation/views/features/store/product/product_view_model.dart';
import 'package:vcc/presentation/views/features/store/store_view_model.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/product_by_horizontal_widget.dart';

class ProductPage extends StatefulHookConsumerWidget {
  const ProductPage({super.key});

  @override
  ConsumerState<ProductPage> createState() => _ProductPageState();
}

class _ProductPageState extends ConsumerState<ProductPage> {
  @override
  void initState() {
    Future(() {
      if (ref.watch(storeProvider).addressDefault != null) {
        ref.read(productProvider.notifier).initData();
      }
    });
    super.initState();
  }

  Future refreshData() async {
    ref.read(productProvider.notifier).initData();
  }

  @override
  Widget build(BuildContext context) {
    final productState = ref.watch(productProvider);
    return Container(
      color: BaseColors.backgroundWhite,
      child: Column(
        children: <Widget>[
          _buildCategories(
            categories: productState.categories ?? [],
            categorySelected: productState.categorySelected,
          ),
          DividerWidget(
            height: 4,
            color: BaseColors.backgroundGray,
          ),
          Expanded(
            child: _buildListProduct(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategories({
    required List<CategoryEntity> categories,
    CategoryEntity? categorySelected,
  }) {
    return SizedBox(
      height: 150,
      child: Padding(
        padding: const EdgeInsets.only(
          top: 24,
        ),
        child: ListView.separated(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          scrollDirection: Axis.horizontal,
          itemCount: categories.length,
          separatorBuilder: (_, __) => const SizedBox(width: 8),
          itemBuilder: (context, index) {
            final item = categories[index];
            return Semantics(
              identifier: "itemCategory_${item.code ?? index}",
              child: InkWellWidget(
                onTap: () {
                  ref.read(productProvider.notifier).changeCategory(item);
                },
                child: SizedBox(
                  width: 76,
                  child: Column(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                            ),
                            child: ImageWidget(
                              item.imageUrl ?? '',
                              size: const Size(60, 60),
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            item.name ?? '',
                            style: UITextStyle.caption1SemiBold.copyWith(
                              color: BaseColors.textLabel,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                      const Spacer(),
                      (categorySelected?.code == item.code)
                          ? Container(
                              height: 2,
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(1),
                                  topRight: Radius.circular(1),
                                ),
                                color: BaseColors.primary,
                              ),
                            )
                          : const SizedBox(),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildListProduct() {
    final state = ref.watch(productProvider);

    if (state.loadProductStatus == LoadStatus.loading) {
      return Semantics(
        identifier: "loadingProductList",
        child: const LoadingIndicatorWidget(),
      );
    } else if (state.loadProductStatus == LoadStatus.failure) {
      return Semantics(
        identifier: "errorProductList",
        child: EmptyListWidget(
          title: state.message ?? 'Đã có lỗi xảy ra!',
          onRefresh: refreshData,
        ),
      );
    } else {
      if ((state.products ?? []).isEmpty) {
        return Semantics(
          identifier: "emptyProductList",
          child: EmptyListWidget(
            title: "Không tìm thấy sản phẩm nào",
            onRefresh: refreshData,
          ),
        );
      } else {
        return Semantics(
          identifier: "productListView",
          child: ListView.separated(
            padding: const EdgeInsets.only(
              bottom: 24,
            ),
            itemCount: state.products?.length ?? 0,
            separatorBuilder: (context, index) {
              return DividerWidget(
                height: 4,
                color: BaseColors.backgroundGray,
              );
            },
            itemBuilder: (context, index) {
              final item = state.products![index];

            return Column(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        child: Semantics(
                          identifier: 'txtTitleCategory',
                          child: Text(
                            ('${item.name} (${item.total})'),
                            style: UITextStyle.body1SemiBold.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Semantics(
                        identifier: 'btnViewAll',
                        child: AppTextButton(
                          title: "Xem tất cả",
                          titleStyle: UITextStyle.caption1Medium.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                          iconRight: MyAssets.icons.iconArrowRightS20.svg(),
                          padding: const EdgeInsets.fromLTRB(8, 8, 0, 8),
                          onTap: () {
                            context.push(
                              RouterPaths.allProducts,
                              extra: AllProductsArguments(
                                categoryId: item.categoryId!,
                                products: item.data,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 252,
                  width: double.infinity,
                  child: ListView.separated(
                    shrinkWrap: true,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                    ),
                    physics: const BouncingScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    itemCount: item.data?.length ?? 0,
                    separatorBuilder: (_, __) => const SizedBox(width: 12),
                    itemBuilder: (context, index) {
                      var product = item.data![index];
                      return ProductByHorizontalWidget(
                        product: product,
                        width: 140,
                      );
                    },
                  ),
                ),
              ],
            );
          },
          ),
        );
      }
    }
  }
}
