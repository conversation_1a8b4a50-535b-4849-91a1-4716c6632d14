import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';

/// Widget để validate authentication
/// Nếu user chưa đăng nhập sẽ redirect đến login
/// Sau khi login xong sẽ quay lại màn hình hiện tại
class AuthValidationWidget extends ConsumerStatefulWidget {
  /// Widget con sẽ được hiển thị khi user đã đăng nhập
  final Widget child;
  
  /// Callback được gọi khi user đăng nhập thành công (optional)
  final VoidCallback? onAuthenticated;
  
  /// Có hiển thị loading indicator khi đang check auth không
  final bool showLoadingIndicator;
  
  /// Custom loading widget (optional)
  final Widget? loadingWidget;

  const AuthValidationWidget({
    super.key,
    required this.child,
    this.onAuthenticated,
    this.showLoadingIndicator = true,
    this.loadingWidget,
  });

  @override
  ConsumerState<AuthValidationWidget> createState() => _AuthValidationWidgetState();
}

class _AuthValidationWidgetState extends ConsumerState<AuthValidationWidget> {
  bool _isCheckingAuth = true;
  bool _isAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _checkAuthentication();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Re-check authentication when returning from other screens
    _checkAuthentication();
  }

  Future<void> _checkAuthentication() async {
    setState(() {
      _isCheckingAuth = true;
    });

    try {
      // Check if user is logged in
      final userInfo = GlobalData.instance.userInfo;
      final isLoggedIn = userInfo != null;

      if (isLoggedIn) {
        setState(() {
          _isAuthenticated = true;
          _isCheckingAuth = false;
        });
        
        // Call onAuthenticated callback if provided
        widget.onAuthenticated?.call();
      } else {
        setState(() {
          _isAuthenticated = false;
          _isCheckingAuth = false;
        });
        
        // Redirect to login with current location as return path
        _redirectToLogin();
      }
    } catch (e) {
      setState(() {
        _isAuthenticated = false;
        _isCheckingAuth = false;
      });
      
      // On error, also redirect to login
      _redirectToLogin();
    }
  }

  void _redirectToLogin() {
    if (!mounted) return;
    
    // Get current location to return after login
    final currentLocation = GoRouterState.of(context).uri.toString();
    
    // Navigate to login with return path
    context.pushReplacement(
      RouterPaths.login,
      extra: {
        'returnPath': currentLocation,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingAuth) {
      if (widget.showLoadingIndicator) {
        return widget.loadingWidget ?? 
               const Scaffold(
                 body: Center(
                   child: LoadingIndicatorWidget(),
                 ),
               );
      } else {
        return const SizedBox.shrink();
      }
    }

    if (_isAuthenticated) {
      return widget.child;
    }

    // This should not be reached as we redirect to login
    // But just in case, show empty widget
    return const SizedBox.shrink();
  }
}

/// Extension method để dễ dàng wrap widget với auth validation
extension AuthValidationExtension on Widget {
  /// Wrap widget với auth validation
  Widget requireAuth({
    VoidCallback? onAuthenticated,
    bool showLoadingIndicator = true,
    Widget? loadingWidget,
  }) {
    return AuthValidationWidget(
      onAuthenticated: onAuthenticated,
      showLoadingIndicator: showLoadingIndicator,
      loadingWidget: loadingWidget,
      child: this,
    );
  }
}

/// Utility class để check auth status
class AuthValidator {
  /// Check if user is currently authenticated
  static bool get isAuthenticated {
    return GlobalData.instance.userInfo != null;
  }
  
  /// Get current user info
  static dynamic get currentUser {
    return GlobalData.instance.userInfo;
  }
  
  /// Check if user has specific role/permission
  static bool hasRole(String role) {
    final userInfo = GlobalData.instance.userInfo;
    if (userInfo == null) return false;
    
    // Add your role checking logic here
    // Example: return userInfo.roles?.contains(role) ?? false;
    return true; // Placeholder
  }
  
  /// Check if user is internal staff
  static bool get isInternalUser {
    final userInfo = GlobalData.instance.userInfo;
    if (userInfo == null) return false;
    
    // Add your internal user checking logic here
    // Example: return userInfo.userType == UserCompanyType.internal;
    return true; // Placeholder
  }
}
