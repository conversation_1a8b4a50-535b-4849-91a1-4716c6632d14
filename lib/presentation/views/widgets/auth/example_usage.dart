import 'package:flutter/material.dart';
import 'package:vcc/presentation/views/widgets/auth/auth_validation_widget.dart';
import 'package:vcc/presentation/views/widgets/auth/auth_wrapper.dart';

/// Example usage của Auth Validation Widgets
class AuthExampleUsage {
  
  /// Example 1: Basic auth validation
  static Widget basicAuthExample() {
    return AuthValidationWidget(
      child: Scaffold(
        appBar: AppBar(title: const Text('Protected Page')),
        body: const Center(
          child: Text('This page requires authentication'),
        ),
      ),
    );
  }
  
  /// Example 2: Using extension method
  static Widget extensionExample() {
    return Scaffold(
      appBar: AppBar(title: const Text('Protected Page')),
      body: const Center(
        child: Text('This page requires authentication'),
      ),
    ).requireAuth();
  }
  
  /// Example 3: Auth with role requirement
  static Widget roleBasedExample() {
    return AuthWrapper(
      requiredRole: 'admin',
      child: Scaffold(
        appBar: AppBar(title: const Text('Admin Page')),
        body: const Center(
          child: Text('This page requires admin role'),
        ),
      ),
    );
  }
  
  /// Example 4: Internal users only
  static Widget internalUserExample() {
    return AuthWrapper(
      requireInternal: true,
      child: Scaffold(
        appBar: AppBar(title: const Text('Internal Page')),
        body: const Center(
          child: Text('This page is for internal users only'),
        ),
      ),
    );
  }
  
  /// Example 5: Custom loading widget
  static Widget customLoadingExample() {
    return AuthValidationWidget(
      loadingWidget: const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Checking authentication...'),
            ],
          ),
        ),
      ),
      child: Scaffold(
        appBar: AppBar(title: const Text('Protected Page')),
        body: const Center(
          child: Text('Custom loading example'),
        ),
      ),
    );
  }
  
  /// Example 6: With authentication callback
  static Widget callbackExample() {
    return AuthValidationWidget(
      onAuthenticated: () {
        print('User authenticated successfully!');
        // Track analytics, load user data, etc.
      },
      child: Scaffold(
        appBar: AppBar(title: const Text('Protected Page')),
        body: const Center(
          child: Text('Page with auth callback'),
        ),
      ),
    );
  }
  
  /// Example 7: Using AuthenticatedPage helper
  static Widget helperExample() {
    return AuthenticatedPage.create(
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('Helper Example')),
        body: const Center(
          child: Text('Created using AuthenticatedPage helper'),
        ),
      ),
    );
  }
  
  /// Example 8: Internal user with helper
  static Widget internalHelperExample() {
    return AuthenticatedPage.createForInternal(
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('Internal Helper')),
        body: const Center(
          child: Text('Internal user page using helper'),
        ),
      ),
    );
  }
  
  /// Example 9: Role-based with helper
  static Widget roleHelperExample() {
    return AuthenticatedPage.createWithRole(
      role: 'manager',
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('Manager Page')),
        body: const Center(
          child: Text('Manager role required'),
        ),
      ),
    );
  }
  
  /// Example 10: Complex example with all features
  static Widget complexExample() {
    return AuthWrapper(
      requiredRole: 'admin',
      requireInternal: true,
      loadingWidget: const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.blue),
              SizedBox(height: 16),
              Text('Validating admin access...'),
            ],
          ),
        ),
      ),
      onAuthenticated: () {
        print('Admin user authenticated!');
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Admin Dashboard'),
          backgroundColor: Colors.red,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.admin_panel_settings, size: 64),
              SizedBox(height: 16),
              Text(
                'Admin Dashboard',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('Requires admin role + internal user'),
            ],
          ),
        ),
      ),
    );
  }
}

/// Example page sử dụng auth validation
class ExampleProtectedPage extends StatelessWidget {
  const ExampleProtectedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Protected Page')),
      body: const Center(
        child: Text('This page requires authentication'),
      ),
    ).requireAuth(
      onAuthenticated: () {
        print('User authenticated for ExampleProtectedPage');
      },
    );
  }
}

/// Example admin page
class ExampleAdminPage extends StatelessWidget {
  const ExampleAdminPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Admin Page')),
      body: const Center(
        child: Text('Admin access required'),
      ),
    ).wrapWithRole('admin');
  }
}

/// Example internal page
class ExampleInternalPage extends StatelessWidget {
  const ExampleInternalPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Internal Page')),
      body: const Center(
        child: Text('Internal users only'),
      ),
    ).wrapForInternal();
  }
}

/// Example conditional UI based on auth
class ExampleConditionalPage extends StatelessWidget {
  const ExampleConditionalPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Conditional Page')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (AuthValidator.isAuthenticated) ...[
              const Text('Welcome back!'),
              Text('User: ${AuthValidator.currentUser?.fullName ?? 'Unknown'}'),
              const SizedBox(height: 16),
              if (AuthValidator.hasRole('admin'))
                const Text('You have admin privileges'),
              if (AuthValidator.isInternalUser)
                const Text('Internal user access'),
            ] else ...[
              const Text('Please log in to continue'),
              ElevatedButton(
                onPressed: () {
                  // Navigate to login
                },
                child: const Text('Login'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
