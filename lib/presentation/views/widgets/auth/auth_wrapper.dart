import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/presentation/views/widgets/auth/auth_validation_widget.dart';

/// Wrapper widget để bọc các page cần authentication
/// Tự động handle redirect và return logic
class AuthWrapper extends ConsumerStatefulWidget {
  /// Widget con sẽ được hiển thị khi user đã đăng nhập
  final Widget child;
  
  /// <PERSON><PERSON> yêu cầu role cụ thể không
  final String? requiredRole;
  
  /// <PERSON><PERSON> yêu cầu là internal user không
  final bool requireInternal;
  
  /// Custom loading widget
  final Widget? loadingWidget;
  
  /// Callback khi authentication thành công
  final VoidCallback? onAuthenticated;

  const AuthWrapper({
    super.key,
    required this.child,
    this.requiredRole,
    this.requireInternal = false,
    this.loadingWidget,
    this.onAuthenticated,
  });

  @override
  ConsumerState<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends ConsumerState<AuthWrapper> {
  @override
  Widget build(BuildContext context) {
    return AuthValidationWidget(
      loadingWidget: widget.loadingWidget,
      onAuthenticated: () {
        _checkPermissions();
        widget.onAuthenticated?.call();
      },
      child: widget.child,
    );
  }
  
  void _checkPermissions() {
    final userInfo = GlobalData.instance.userInfo;
    if (userInfo == null) return;
    
    // Check role if required
    if (widget.requiredRole != null) {
      // Add your role checking logic here
      // if (!userInfo.roles?.contains(widget.requiredRole) ?? true) {
      //   context.pushReplacement(RouterPaths.unauthorized);
      //   return;
      // }
    }
    
    // Check internal user if required
    if (widget.requireInternal) {
      // Add your internal user checking logic here
      // if (userInfo.userType != UserCompanyType.internal) {
      //   context.pushReplacement(RouterPaths.unauthorized);
      //   return;
      // }
    }
  }
}

/// Builder function để tạo authenticated page
typedef AuthenticatedPageBuilder = Widget Function(BuildContext context);

/// Helper class để tạo authenticated pages
class AuthenticatedPage {
  /// Tạo page yêu cầu authentication
  static Widget create({
    required AuthenticatedPageBuilder builder,
    String? requiredRole,
    bool requireInternal = false,
    Widget? loadingWidget,
    VoidCallback? onAuthenticated,
  }) {
    return Builder(
      builder: (context) => AuthWrapper(
        requiredRole: requiredRole,
        requireInternal: requireInternal,
        loadingWidget: loadingWidget,
        onAuthenticated: onAuthenticated,
        child: builder(context),
      ),
    );
  }
  
  /// Tạo page cho internal users only
  static Widget createForInternal({
    required AuthenticatedPageBuilder builder,
    Widget? loadingWidget,
    VoidCallback? onAuthenticated,
  }) {
    return create(
      builder: builder,
      requireInternal: true,
      loadingWidget: loadingWidget,
      onAuthenticated: onAuthenticated,
    );
  }
  
  /// Tạo page với role cụ thể
  static Widget createWithRole({
    required AuthenticatedPageBuilder builder,
    required String role,
    Widget? loadingWidget,
    VoidCallback? onAuthenticated,
  }) {
    return create(
      builder: builder,
      requiredRole: role,
      loadingWidget: loadingWidget,
      onAuthenticated: onAuthenticated,
    );
  }
}

/// Extension để dễ dàng wrap page với auth
extension AuthWrapperExtension on Widget {
  /// Wrap với auth validation
  Widget wrapWithAuth({
    String? requiredRole,
    bool requireInternal = false,
    Widget? loadingWidget,
    VoidCallback? onAuthenticated,
  }) {
    return AuthWrapper(
      requiredRole: requiredRole,
      requireInternal: requireInternal,
      loadingWidget: loadingWidget,
      onAuthenticated: onAuthenticated,
      child: this,
    );
  }
  
  /// Wrap cho internal users only
  Widget wrapForInternal({
    Widget? loadingWidget,
    VoidCallback? onAuthenticated,
  }) {
    return wrapWithAuth(
      requireInternal: true,
      loadingWidget: loadingWidget,
      onAuthenticated: onAuthenticated,
    );
  }
  
  /// Wrap với role cụ thể
  Widget wrapWithRole(
    String role, {
    Widget? loadingWidget,
    VoidCallback? onAuthenticated,
  }) {
    return wrapWithAuth(
      requiredRole: role,
      loadingWidget: loadingWidget,
      onAuthenticated: onAuthenticated,
    );
  }
}
