import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/presentation/views/routers/paths.dart';

/// Guard để protect routes yêu cầu authentication
class AuthGuard {
  /// Check if user is authenticated and redirect if needed
  static String? redirectIfNotAuthenticated(
    BuildContext context,
    GoRouterState state,
  ) {
    final userInfo = GlobalData.instance.userInfo;
    final isLoggedIn = userInfo != null;
    
    if (!isLoggedIn) {
      // Store current path to return after login
      final currentPath = state.uri.toString();
      
      // Return login path with current path as query parameter
      return '${RouterPaths.login}?returnPath=${Uri.encodeComponent(currentPath)}';
    }
    
    // User is authenticated, allow access
    return null;
  }
  
  /// Check if user has specific role and redirect if needed
  static String? redirectIfNoRole(
    BuildContext context,
    GoRouterState state,
    String requiredRole,
  ) {
    // First check authentication
    final authRedirect = redirectIfNotAuthenticated(context, state);
    if (authRedirect != null) return authRedirect;

    // Add your role checking logic here
    // Example:
    // final userInfo = GlobalData.instance.userInfo;
    // if (!userInfo.roles?.contains(requiredRole) ?? true) {
    //   return RouterPaths.unauthorized;
    // }
    
    return null;
  }
  
  /// Check if user is internal staff
  static String? redirectIfNotInternal(
    BuildContext context,
    GoRouterState state,
  ) {
    // First check authentication
    final authRedirect = redirectIfNotAuthenticated(context, state);
    if (authRedirect != null) return authRedirect;

    // Add your internal user checking logic here
    // Example:
    // final userInfo = GlobalData.instance.userInfo;
    // if (userInfo.userType != UserCompanyType.internal) {
    //   return RouterPaths.unauthorized;
    // }
    
    return null;
  }
}

/// Mixin để dễ dàng sử dụng auth guard trong routes
mixin AuthGuardMixin {
  /// Protect route with authentication
  String? requireAuth(BuildContext context, GoRouterState state) {
    return AuthGuard.redirectIfNotAuthenticated(context, state);
  }
  
  /// Protect route with specific role
  String? requireRole(BuildContext context, GoRouterState state, String role) {
    return AuthGuard.redirectIfNoRole(context, state, role);
  }
  
  /// Protect route for internal users only
  String? requireInternal(BuildContext context, GoRouterState state) {
    return AuthGuard.redirectIfNotInternal(context, state);
  }
}
